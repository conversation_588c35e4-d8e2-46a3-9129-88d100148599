import os
import json
import requests
# from dashscope import Generation
import random

from dashscope import Generation
from openai import OpenAI



def get_prompt(path,query,arr1,arr2,arr3,arr4,datetime):
    root_path = os.path.dirname(os.path.abspath(__file__))

    prompt = "".join( open(root_path + "/../prompts/" + path, "r", encoding = "utf-8").readlines() )
    prompt = prompt.replace("{query}", query)
    prompt = prompt.replace("{entitytypelist}", arr1)
    prompt = prompt.replace("{intensionlist}", arr2)
    prompt = prompt.replace("{conditionlist}", arr3)
    prompt = prompt.replace("{Metrics}", arr4)
    prompt = prompt.replace("{datetime}", datetime)

    # 构建 JSON 文件路径
    base_name = path.replace("_template.txt", "")
    json_file = os.path.join("template", base_name + ".json")
    if os.path.exists(json_file):
        with open(json_file, "r", encoding="utf-8") as f:
            json_data = json.load(f)  # 读取 JSON 内容
            json_str = json.dumps(json_data, ensure_ascii=False)  # 转为字符串
            lstname = "{" + base_name + "_list}"
            prompt = prompt.replace(lstname, json_str)  # 替换 {{json名}}

    return prompt


def qwen7b(messages):
    url ="http://192.168.232.20:9018/vllm/predict_async"
    params = {"stream":False,"temperature":"0.1","messages":messages}
    response = requests.request("POST", url, data=json.dumps(params), stream=False,headers={})
    return response.json()["result"]

def qwen14b(messages):
    url ="http://192.168.186.11:8008/vllm/predict_async"
    params = {"stream":False,"temperature":"0.1","messages":messages}
    response = requests.request("POST", url, data=json.dumps(params), stream=False,headers={})
    return response.json()["result"]

def deepseek7b(messages):
    url ="http://192.168.186.11:44949/vllm/predict_async"
    params = {"stream":False,"temperature":"0.8","messages":messages}
    response = requests.request("POST", url, data=json.dumps(params), stream=False,headers={})
    return response.json()["result"]

def qwenMax(messages):
    model = "qwq-32b"
    api_key = "***********************************"
    seed =  random.randint(1, 10000)
    response = Generation.call(
        model,
        messages=messages,
        api_key=api_key,
        seed=seed,  # set the random seed, optional, default to 1234 if not set
        result_format='message',  # set the result to be "message"  format.
        temperature=0.7,
        enable_search=True
        # tools=changeQwenTools(tools)
    )

    return response.output.choices[0]['message']['content']

def qwenMax2(messages):
    model = "qwen-max"
    api_key = "***********************************"
    seed =  random.randint(1, 10000)
    response = Generation.call(
        model,
        messages=messages,
        api_key=api_key,
        seed=seed,  # set the random seed, optional, default to 1234 if not set
        result_format='message',  # set the result to be "message"  format.
        temperature=0.7,
        # tools=changeQwenTools(tools)
    )

    return response.output.choices[0]['message']['content']

def qwen72B_sli(messages):
    url = "https://api.siliconflow.cn/v1/chat/completions"

    payload = {
        "model": "Qwen/QwQ-32B",
        "messages": messages,
        "extra_body":{"enable_thinking": False},
        "stream":True
    }
    headers = {
        "Authorization": "Bearer sk-hzixvrxqynemwxcicihfsasslxrjjycyyyekcekldoqjwqes",
        "Content-Type": "application/json"
    }

    response = requests.request("POST", url, json=payload, headers=headers)
    result = json.loads(response.text)
    return result['choices'][0]['message']['content']

def qwen72B(messages):
    model = "qwen2.5-72b-instruct"
    api_key = "***********************************"
    seed =  random.randint(1, 10000)
    response = Generation.call(
        model,
        messages=messages,
        api_key=api_key,
        seed=seed,  # set the random seed, optional, default to 1234 if not set
        result_format='message',  # set the result to be "message"  format.
        temperature=0.3,
        # tools=changeQwenTools(tools)
    )
    print('API返回内容:', response)
    return response.output.choices[0]['message']['content']

def qwen14B(messages):
    model = "qwen2.5-72b-instruct"
    api_key = "***********************************"
    seed =  random.randint(1, 10000)
    response = Generation.call(
        model,
        messages=messages,
        api_key=api_key,
        seed=seed,  # set the random seed, optional, default to 1234 if not set
        result_format='message',  # set the result to be "message"  format.
        temperature=0.3,
        # tools=changeQwenTools(tools)
    )

    return response.output.choices[0]['message']['content']

def qwen3(messages):
    model = "qwen3-32b"
    api_key = "sk-c342324d10474284abc452d71d0dd105"
    seed =  random.randint(1, 10000)
    response = Generation.call(
        model,
        messages=messages,
        api_key=api_key,
        seed=seed,  # set the random seed, optional, default to 1234 if not set
        result_format='message',  # set the result to be "message"  format.
        temperature=0.3,
        enable_thinking =False,
        stream=False,
        stop='Observation'
    )

    return response.output.choices[0]['message']['content']

def deepseekv3(messages):
    model = "deepseek-v3"
    api_key = "***********************************"
    seed =  random.randint(1, 10000)
    response = Generation.call(
        model,
        messages=messages,
        api_key=api_key,
        seed=seed,  # set the random seed, optional, default to 1234 if not set
        result_format='message',  # set the result to be "message"  format.
        temperature=0.7,
        # tools=changeQwenTools(tools)
    )

    return response.output.choices[0]['message']['content']

client = None

def callKimi(messages) -> str:

    global client
    if client is None:
        client = OpenAI(
            api_key = "sk-NOOv7U6XgWdCwLmkqVbnC14YIJjvzR1ZQNWezNJEC9L29j3S", # 在这里将 MOONSHOT_API_KEY 替换为你从 Kimi 开放平台申请的 API Key
            base_url = "https://api.moonshot.cn/v1",
        )
    # 我们将用户最新的问题构造成一个 message（role=user），并添加到 messages 的尾部
    # messages.append({
    #     "role": "user",
    #     "content": input,
    # })

    # 携带 messages 与 Kimi 大模型对话
    completion = client.chat.completions.create(
        model="moonshot-v1-8k",
        messages=messages,
        temperature=0,
    )

    # 通过 API 我们获得了 Kimi 大模型给予我们的回复消息（role=assistant）
    assistant_message = completion.choices[0].message

    # 为了让 Kimi 大模型拥有完整的记忆，我们必须将 Kimi 大模型返回给我们的消息也添加到 messages 中
    messages.append(assistant_message)

    return assistant_message.content


def get_prompt16(path,query,bodyType,registrationGuidelines):
    root_path = os.path.dirname(os.path.abspath(__file__))

    prompt = "".join( open(root_path + "/../prompts/" + path, "r", encoding = "utf-8").readlines() )
    prompt = prompt.replace("{query}", query)
    prompt = prompt.replace("{bodyType}", bodyType)
    prompt = prompt.replace("{registrationGuidelines}", registrationGuidelines)
    # 构建 JSON 文件路径
    base_name = path.replace("_template.txt", "")
    json_file = os.path.join("template", base_name + ".json")
    if os.path.exists(json_file):
        with open(json_file, "r", encoding="utf-8") as f:
            json_data = json.load(f)  # 读取 JSON 内容
            json_str = json.dumps(json_data, ensure_ascii=False)  # 转为字符串
            lstname = "{" + base_name + "_list}"
            prompt = prompt.replace(lstname, json_str)  # 替换 {{json名}}

    return prompt



def get_rag3_prompt(path,query,Aresult,Bresult):
    root_path = os.path.dirname(os.path.abspath(__file__))

    prompt = "".join( open(root_path + "/../RAGV3/" + path, "r", encoding = "utf-8").readlines() )
    prompt = prompt.replace("{query}", query)
    prompt = prompt.replace("{Aresult}", Aresult)
    prompt = prompt.replace("{Bresult}", Bresult)
    # 构建 JSON 文件路径
    base_name = path.replace("_template.txt", "")
    json_file = os.path.join("template", base_name + ".json")
    if os.path.exists(json_file):
        with open(json_file, "r", encoding="utf-8") as f:
            json_data = json.load(f)  # 读取 JSON 内容
            json_str = json.dumps(json_data, ensure_ascii=False)  # 转为字符串
            lstname = "{" + base_name + "_list}"
            prompt = prompt.replace(lstname, json_str)  # 替换 {{json名}}

    return prompt

def get_current_weather(arguments):
    # 定义备选的天气条件列表
    weather_conditions = ["晴天", "多云", "雨天"]
    # 随机选择一个天气条件
    random_weather = random.choice(weather_conditions)
    # 从 JSON 中提取位置信息
    location = arguments["location"]
    # 返回格式化的天气信息
    return f"{location}今天是{random_weather}。"

def qwen3tool(messages):
    model = "qwen3-32b"
    api_key = "sk-ac5a44787f8c45778d545ffb1ad295f7"
    seed =  random.randint(1, 10000)
    tools = [
        {
            "type": "function",
            "function": {
                "name": "get_current_weather",
                "description": "当你想查询指定城市的天气时非常有用。",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "location": {
                            "type": "string",
                            "description": "城市或县区，比如北京市、杭州市、余杭区等。",
                        }
                    },
                    "required": ["location"]
                }
            }
        }
    ]
    response = Generation.call(
        model,
        messages=messages,
        api_key=api_key,
        seed=seed,  # set the random seed, optional, default to 1234 if not set
        result_format='message',  # set the result to be "message"  format.
        temperature=0.3,
        enable_thinking =False,
        tools=tools
    )

    if response.output.choices[0].finish_reason == 'tool_calls':
        result = response.output.choices[0].message['tool_calls'][0].get('function')
        functionname = result.get('name')
        params = result.get('arguments')
        return eval(f"{functionname}({params})")
    else:
        return response.output.choices[0]['message']['content']

if __name__ == "__main__":
    messages = [
        {
            "role": "user",
            "content": "上海的天气怎么样"
        }
    ]
    print(qwen3tool(messages))

