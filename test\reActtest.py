import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from steps.tools import get_prompt, qwen72B_sli,qwen3

prompt = """你是一个可以推理和行动的问题拆解与回答助手，你可以使用以下工具：

统计(子问题):用户询问关于某类数据的统计量（数量、同比、环比、排名、占比等），或数据在一定时间范围内的变化趋势。
明细(子问题):用户询问特定项目或事件的具体细节信息，问答助手会针对该问题给出详细且具体的回答，如项目的某个环节进展情况、涉及的具体数据等。
报表(子问题):用户直接索要某个特定时间的特定类型报表，希望获得包含相关数据及分析的完整报表呈现。
专报(子问题):用户请求提供针对某一主题（如企业招标采购数据年度分析）的专门报告，通常这类报告涉及深入的分析和总结 。

用户历史咨询记录：
用户：项目数量最多的地区是哪一个？这个地区下各项目类型的预算金额排名如何？
客服：项目数量最多的地区是华东地区。该地区下各项目类型的预算金额排名为：公司A 3.2亿，公司B 2.1亿，公司C 1.7亿。
用户：那这里最差的公司是啥？这个公司法人             是谁？
客服：这里预算金额最少的公司是公司C，其法人是赵伟。

使用下面的格式进行推理和行动：

Question：你必须回答的输入问题
Thought：你应当始终思考接下来该怎么做。**重要：首先仔细分析历史咨询记录中是否已经包含了回答当前问题所需的信息，如果历史记录中已有相关数据，请直接利用这些信息进行推理，避免重复查询已知信息。**然后分析用户原始问题是否需要拆解，如果有多个子问题，请逐个处理，**必须确保所有子问题都得到处理后才能给出最终答案**。
Action：要执行的操作，应当是统计、明细、报表、专报之一
Action Input：该操作的输入，也就是子问题，子问题尽量一句话描述，不要断句，仅包含一个问题
Observation：该操作的执行结果。action未执行成功会返回“意图解析失败”（这种情况下，你可以跳过当前问题直接进入下一个问题，如果是最后一个问题或者下一个问题依赖当前问题结果直接结束）。
...（可以重复上述 Thought / Action / Action Input / Observation 这一组零次或多次）
Thought：我现在知道最终答案了
Final Answer：对原始输入问题的最终回答

开始！

Question：2024年进场的项目数量是多少？占比是多少？
Thought：用户询问2024年进场的项目数量以及其占比，这是两个子问题。历史咨询记录中并未提及2024年的相关信息，因此需要分别查询这两个问题。
Action：统计
Action Input：2024年进场的项目数量是多少？
Observation: 2024进场项目数量 187
Thought：已经获得了2024年进场项目数量是187个，现在需要查询第二个子问题：占比是多少。
Action：统计
Action Input：2024年进场项目数量占总项目数量的比例是多少？
Observation: 2024年进场项目占比 23.5%
Thought：我现在知道最终答案了
Final Answer：2024年进场的项目数量是187个，占比是23.5%。
"""


messages = [
    {
        "role": "user",
        "content": prompt
    }
]

# 第一轮调用
response1 = qwen3(messages)
print("=== 第一轮响应 ===")
print(response1)

# 模拟添加 Observation 并继续对话
messages.append({
    "role": "assistant",
    "content": response1
})
messages.append({
    "role": "user",
    "content": "Observation: 2024进场项目数量 187"
})

print("\n=== 第二轮响应 ===")
response2 = qwen3(messages)
print(response2)


prompt2="""你是数据分析任务规划专家。请将用户问题分解为结构化的执行任务。

用户问题：{user_question}

请严格按照以下JSON格式输出，不要添加任何其他文字：

{{
  "tasks": [
    {{
      "task_id": "T1",
      "description": "具体任务描述",
      "type": "query|analysis|calculation",
      "depends_on": [],
      "output_fields": ["field1", "field2"],
      "is_terminal": false
    }},
    {{
      "task_id": "T2", 
      "description": "具体任务描述",
      "type": "query|analysis|calculation",
      "depends_on": ["T1"],
      "required_inputs": ["T1.field1"],
      "output_fields": ["field3", "field4"],
      "is_terminal": true
    }}
  ],
  "execution_plan": {{
    "parallel_groups": [["T1"], ["T2"]],
    "execution_order": ["T1", "T2"]
  }}
}}

"""


prompt3 = """你是一个可以推理和行动的智能问答助手，你能结合自己的推理与外部工具接口，逐步回答复杂的问题。

当你需要获得外部信息时，可以调用工具（action），语法格式如下：

Action: action_name[参数]

执行完 action 后，我会返回 Observation，内容是你调用的结果。

你可以继续思考下一步要做什么，直到问题完成。

请根据下面的问题开始推理和行动：

Question: 2024年销售额排名第一的地区是哪里？这个地区下各个子公司的销售额排名情况如何？

Thought1:为了回答这个问题，我需要采取以下步骤：

1. 获取2024年销售额数据，确定销售额排名第一的地区。
2. 然后获取该地区下各个子公司的销售额数据，并进行排名。

首先，我需要调用一个工具来获取2024年的销售额数据。假设这个工具叫做 `get_sales_data`，并且它能够返回按地区划分的销售额信息。

Action: get_sales_data[year=2024]
Observation: 华东地区 销售额 8.7亿

Thought2: 根据获取到的数据，2024年销售额排名第一的地区是华东地区。接下来，我需要调用另一个工具来获取华东地区下各个子公司的销售额数据，并进行排名。

Action: get_subcompany_sales_data[region=华东地区]
Observation: 公司A: 3.2亿, 公司B: 2.1亿, 公司C: 1.7亿
"""

prompt4 = """你是一个可以推理和行动的问题拆解与回答助手，你可以使用以下工具：

统计(子问题):用户围绕特定时间段、特定地区、特定版本等条件，询问关于某类数据的统计量（数量、同比、环比等），或数据在一定时间范围内的变化趋势。
明细(子问题):用户询问特定项目或事件的具体细节信息，问答助手会针对该问题给出详细且具体的回答，如项目的某个环节进展情况、涉及的具体数据等。
报表(子问题):用户直接索要某个特定时间的特定类型报表，希望获得包含相关数据及分析的完整报表呈现。
专报(子问题):用户请求提供针对某一主题（如企业招标采购数据年度分析）的专门报告，通常这类报告涉及深入的分析和总结 。

用户历史咨询记录：
用户：项目数量最多的地区是哪一个？这个地区下各项目类型的预算金额排名如何？
客服：项目数量最多的地区是华东地区。该地区下各项目类型的预算金额排名为：公司A 3.2亿，公司B 2.1亿，公司C 1.7亿。

使用下面的格式进行推理和行动：

Question：你必须回答的输入问题
Thought：你应当始终思考接下来该怎么做。**重要：首先仔细分析历史咨询记录中是否已经包含了回答当前问题所需的信息，如果历史记录中已有相关数据，请直接利用这些信息进行推理，避免重复查询已知信息。**然后分析用户原始问题是否需要拆解，如果有多个子问题，请逐个处理。
Action：要执行的操作，应当是统计、明细、报表、专报之一
Action Input：该操作的输入，也就是子问题，子问题尽量一句话描述，不要断句，仅包含一个问题
Observation：该操作的执行结果
...（可以重复上述 Thought / Action / Action Input / Observation 这一组零次或多次）
Thought：我现在知道最终答案了
Final Answer：对原始输入问题的最终回答

开始！

Question：那这里最差的公司是啥？这个公司法人是谁？

"""
